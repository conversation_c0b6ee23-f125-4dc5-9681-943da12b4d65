'use client';
import axios from 'axios';

// Cliente API para el catálogo público
export const catalogApiClient = axios.create({
  baseURL: 'http://localhost:3002/api/catalog',
});

// Interfaces para el catálogo público
export interface PublicProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  rating: number;
  reviewCount: number;
  preparationTime: string;
  isAvailable: boolean;
  tags: string[];
  store: {
    id: string;
    name: string;
    logo: string;
    rating: number;
    deliveryTime: string;
  };
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

export interface PublicStore {
  id: string;
  name: string;
  description: string;
  logo: string;
  coverImage: string;
  rating: number;
  reviewCount: number;
  deliveryTime: string;
  deliveryFee: number;
  minimumOrder: number;
  categories: string[];
  isOpen: boolean;
  location: string;
}

export interface ProductCategory {
  name: string;
  count: number;
}

export interface ProductReview {
  id: string;
  customerName: string;
  rating: number;
  comment: string;
  reply?: string;
  createdAt: string;
}

export interface CartItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  deliveryMethod: 'delivery' | 'pickup';
  subtotal: number;
}

export interface Cart {
  id: string;
  items: CartItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  total: number;
  itemCount: number;
}

export interface CreateOrderData {
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    address?: string;
  };
  deliveryMethod: 'delivery' | 'pickup';
  paymentMethod: string;
  notes?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  estimatedDeliveryTime: string;
  trackingUrl?: string;
}

// Función auxiliar para obtener session ID
const getSessionId = (): string => {
  let sessionId = localStorage.getItem('catalog_session_id');
  if (!sessionId) {
    sessionId = crypto.randomUUID();
    localStorage.setItem('catalog_session_id', sessionId);
  }
  return sessionId;
};

// Función auxiliar para obtener headers con session ID
const getSessionHeaders = () => {
  return {
    'x-session-id': getSessionId(),
    'Content-Type': 'application/json'
  };
};

// ===== PRODUCTOS =====

// Obtener catálogo de productos con filtros
export const getProducts = async (params: {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  storeId?: string;
  sortBy?: 'name' | 'price' | 'rating' | 'popularity';
  sortOrder?: 'asc' | 'desc';
} = {}): Promise<{
  products: PublicProduct[];
  totalPages: number;
  currentPage: number;
  totalProducts: number;
}> => {
  try {
    const response = await catalogApiClient.get('/products', {
      params: {
        page: params.page || 1,
        limit: params.limit || 12,
        search: params.search || '',
        category: params.category || '',
        minPrice: params.minPrice,
        maxPrice: params.maxPrice,
        storeId: params.storeId,
        sortBy: params.sortBy || 'popularity',
        sortOrder: params.sortOrder || 'desc'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
};

// Obtener categorías de productos
export const getProductCategories = async (): Promise<ProductCategory[]> => {
  try {
    const response = await catalogApiClient.get('/products/categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Obtener detalle de un producto
export const getProductDetail = async (productId: string): Promise<PublicProduct> => {
  try {
    const response = await catalogApiClient.get(`/products/${productId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching product detail:', error);
    throw error;
  }
};

// Obtener productos de una tienda específica
export const getStoreProducts = async (
  storeId: string,
  params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}
): Promise<{
  products: PublicProduct[];
  totalPages: number;
  currentPage: number;
  totalProducts: number;
}> => {
  try {
    const response = await catalogApiClient.get(`/stores/${storeId}/products`, {
      params: {
        page: params.page || 1,
        limit: params.limit || 12,
        search: params.search || '',
        category: params.category || '',
        sortBy: params.sortBy || 'popularity',
        sortOrder: params.sortOrder || 'desc'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching store products:', error);
    throw error;
  }
};

// ===== RESEÑAS =====

// Obtener reseñas de un producto
export const getProductReviews = async (
  productId: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  reviews: ProductReview[];
  totalPages: number;
  currentPage: number;
  averageRating: number;
}> => {
  try {
    const response = await catalogApiClient.get(`/reviews/product/${productId}`, {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching product reviews:', error);
    throw error;
  }
};

// Crear una reseña
export const createReview = async (reviewData: {
  productId: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  comment: string;
}): Promise<ProductReview> => {
  try {
    const response = await catalogApiClient.post('/reviews', reviewData);
    return response.data;
  } catch (error) {
    console.error('Error creating review:', error);
    throw error;
  }
};

// ===== CARRITO =====

// Añadir producto al carrito
export const addToCart = async (cartData: {
  productId: string;
  quantity: number;
  deliveryMethod: 'delivery' | 'pickup';
}): Promise<Cart> => {
  try {
    const response = await catalogApiClient.post('/cart', cartData, {
      headers: getSessionHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
};

// Obtener carrito actual
export const getCart = async (): Promise<Cart> => {
  try {
    const response = await catalogApiClient.get('/cart', {
      headers: getSessionHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching cart:', error);
    throw error;
  }
};

// Eliminar item del carrito
export const removeFromCart = async (itemId: string): Promise<Cart> => {
  try {
    const response = await catalogApiClient.delete(`/cart/${itemId}`, {
      headers: getSessionHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error removing from cart:', error);
    throw error;
  }
};

// Actualizar cantidad de item en carrito
export const updateCartItem = async (itemId: string, quantity: number): Promise<Cart> => {
  try {
    const response = await catalogApiClient.patch(`/cart/${itemId}`, 
      { quantity }, 
      { headers: getSessionHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating cart item:', error);
    throw error;
  }
};

// ===== PEDIDOS =====

// Crear pedido desde carrito
export const createOrder = async (orderData: CreateOrderData): Promise<Order> => {
  try {
    const response = await catalogApiClient.post('/orders', orderData, {
      headers: getSessionHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Obtener estado del pedido
export const getOrderStatus = async (orderId: string): Promise<{
  id: string;
  orderNumber: string;
  status: string;
  estimatedDeliveryTime: string;
  trackingUrl?: string;
}> => {
  try {
    const response = await catalogApiClient.get(`/orders/${orderId}/status`);
    return response.data;
  } catch (error) {
    console.error('Error fetching order status:', error);
    throw error;
  }
};
