'use client';
import { ReactNode } from 'react';
import CartProvider from '@/providers/CartProvider';
import ToastProvider from '@/providers/ToastProvider';

interface CatalogLayoutProps {
  children: ReactNode;
}

export default function CatalogLayout({ children }: CatalogLayoutProps) {
  return (
    <ToastProvider>
      <CartProvider>
        {children}
      </CartProvider>
    </ToastProvider>
  );
}
