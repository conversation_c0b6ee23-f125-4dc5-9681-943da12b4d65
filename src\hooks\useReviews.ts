'use client';
import { useState, useEffect, useCallback } from 'react';
import {
  getProductReviews,
  createReview,
  ProductReview
} from '@/services/api/catalog';

// Hook para gestión de reseñas de productos
export const useProductReviews = (productId: string) => {
  const [reviews, setReviews] = useState<ProductReview[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    averageRating: 0
  });

  // Cargar reseñas del producto
  const fetchReviews = useCallback(async (page: number = 1) => {
    if (!productId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await getProductReviews(productId, page, 10);
      
      if (page === 1) {
        setReviews(response.reviews);
      } else {
        // Para paginación, añadir a las reseñas existentes
        setReviews(prev => [...prev, ...response.reviews]);
      }
      
      setPagination({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        averageRating: response.averageRating
      });
    } catch (err: any) {
      setError(err.message || 'Error al cargar reseñas');
      console.error('Error fetching reviews:', err);
    } finally {
      setLoading(false);
    }
  }, [productId]);

  // Cargar más reseñas (paginación)
  const loadMoreReviews = useCallback(() => {
    if (pagination.currentPage < pagination.totalPages && !loading) {
      fetchReviews(pagination.currentPage + 1);
    }
  }, [pagination.currentPage, pagination.totalPages, loading, fetchReviews]);

  // Cargar reseñas iniciales
  useEffect(() => {
    fetchReviews(1);
  }, [fetchReviews]);

  return {
    reviews,
    loading,
    error,
    pagination,
    loadMoreReviews,
    refetch: () => fetchReviews(1),
    hasMoreReviews: pagination.currentPage < pagination.totalPages
  };
};

// Hook para crear reseñas
export const useCreateReview = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const submitReview = useCallback(async (reviewData: {
    productId: string;
    customerName: string;
    customerEmail: string;
    rating: number;
    comment: string;
  }) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      const newReview = await createReview(reviewData);
      setSuccess(true);
      
      return newReview;
    } catch (err: any) {
      setError(err.message || 'Error al enviar reseña');
      console.error('Error creating review:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearStatus = useCallback(() => {
    setError(null);
    setSuccess(false);
  }, []);

  return {
    submitReview,
    loading,
    error,
    success,
    clearStatus
  };
};

// Hook para estadísticas de reseñas
export const useReviewStats = (reviews: ProductReview[]) => {
  const stats = {
    totalReviews: reviews.length,
    averageRating: reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
      : 0,
    ratingDistribution: {
      5: reviews.filter(r => r.rating === 5).length,
      4: reviews.filter(r => r.rating === 4).length,
      3: reviews.filter(r => r.rating === 3).length,
      2: reviews.filter(r => r.rating === 2).length,
      1: reviews.filter(r => r.rating === 1).length,
    },
    hasReviews: reviews.length > 0,
    recentReviews: reviews
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 3)
  };

  return stats;
};

// Hook para validación de formulario de reseña
export const useReviewValidation = () => {
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    rating: 0,
    comment: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    // Validar nombre
    if (!formData.customerName.trim()) {
      newErrors.customerName = 'El nombre es requerido';
    } else if (formData.customerName.trim().length < 2) {
      newErrors.customerName = 'El nombre debe tener al menos 2 caracteres';
    }

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'El email es requerido';
    } else if (!emailRegex.test(formData.customerEmail)) {
      newErrors.customerEmail = 'El email no es válido';
    }

    // Validar rating
    if (formData.rating === 0) {
      newErrors.rating = 'Debes seleccionar una calificación';
    } else if (formData.rating < 1 || formData.rating > 5) {
      newErrors.rating = 'La calificación debe estar entre 1 y 5';
    }

    // Validar comentario
    if (!formData.comment.trim()) {
      newErrors.comment = 'El comentario es requerido';
    } else if (formData.comment.trim().length < 10) {
      newErrors.comment = 'El comentario debe tener al menos 10 caracteres';
    } else if (formData.comment.trim().length > 500) {
      newErrors.comment = 'El comentario no puede exceder 500 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const updateField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando se actualiza
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  const resetForm = useCallback(() => {
    setFormData({
      customerName: '',
      customerEmail: '',
      rating: 0,
      comment: ''
    });
    setErrors({});
  }, []);

  return {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
    isValid: Object.keys(errors).length === 0 && 
             formData.customerName.trim() !== '' &&
             formData.customerEmail.trim() !== '' &&
             formData.rating > 0 &&
             formData.comment.trim() !== ''
  };
};

// Hook para filtrar y ordenar reseñas
export const useReviewFilters = (reviews: ProductReview[]) => {
  const [filters, setFilters] = useState({
    rating: 0, // 0 = todas, 1-5 = filtrar por rating específico
    sortBy: 'newest' as 'newest' | 'oldest' | 'highest' | 'lowest'
  });

  const filteredAndSortedReviews = reviews
    .filter(review => {
      if (filters.rating === 0) return true;
      return review.rating === filters.rating;
    })
    .sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        default:
          return 0;
      }
    });

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      rating: 0,
      sortBy: 'newest'
    });
  }, []);

  return {
    filteredReviews: filteredAndSortedReviews,
    filters,
    updateFilters,
    clearFilters,
    totalFiltered: filteredAndSortedReviews.length
  };
};
