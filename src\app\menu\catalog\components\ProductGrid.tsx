'use client';
import { PublicProduct } from '@/services/api/catalog';
import ProductCard from './ProductCard';

interface ProductGridProps {
  products: PublicProduct[];
  favorites: string[];
  onAddToCart: (productId: string, quantity?: number) => void;
  onToggleFavorite: (productId: string) => void;
  className?: string;
}

export default function ProductGrid({ 
  products, 
  favorites, 
  onAddToCart, 
  onToggleFavorite,
  className = ""
}: ProductGridProps) {
  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">🍽️</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          No hay productos disponibles
        </h3>
        <p className="text-gray-600">
          Intenta ajustar tus filtros o buscar algo diferente
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          isFavorite={favorites.includes(product.id)}
          onAddToCart={() => onAddToCart(product.id)}
          onToggleFavorite={() => onToggleFavorite(product.id)}
        />
      ))}
    </div>
  );
}
