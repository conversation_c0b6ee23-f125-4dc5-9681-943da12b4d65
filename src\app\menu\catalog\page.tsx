'use client';
import { useState } from 'react';
import { useCatalog } from '@/hooks/useCatalog';
import { useCart } from '@/hooks/useCart';
import { useFavorites } from '@/hooks/useCart';
import { useToastContext } from '@/providers/ToastProvider';
import ProductGrid from './components/ProductGrid';
import ProductFilters from './components/ProductFilters';
import SearchBar from './components/SearchBar';
import CartSidebar from './components/CartSidebar';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ToastContainer from '@/components/ui/ToastContainer';
import { Button } from '@/components/ui/button';
import { ShoppingCartIcon, HeartIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';

export default function CatalogPage() {
  const {
    products,
    categories,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    clearFilters,
    changePage
  } = useCatalog();

  // Debug logging
  console.log('Catalog Page State:', {
    productsCount: products.length,
    categoriesCount: categories.length,
    loading,
    error,
    pagination
  });

  const { cart, addItem } = useCart();
  const { favorites, toggleFavorite } = useFavorites();
  const { success, error: showError } = useToastContext();

  const [showFilters, setShowFilters] = useState(false);
  const [showCart, setShowCart] = useState(false);

  const handleAddToCart = async (productId: string, quantity: number = 1) => {
    try {
      await addItem(productId, quantity, 'delivery');
      success('¡Producto añadido!', 'El producto se ha agregado a tu carrito');
    } catch (err) {
      console.error('Error adding to cart:', err);
      showError('Error al añadir', 'No se pudo agregar el producto al carrito');
    }
  };

  const handleToggleFavorite = (productId: string) => {
    toggleFavorite(productId);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error al cargar el catálogo</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-y-3">
              <Button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                Reintentar
              </Button>
              <Button
                onClick={() => window.location.href = '/menu/catalog/test'}
                variant="outline"
                className="w-full"
              >
                Ir a Página de Prueba
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🍕 FoodDelivery
              </h1>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-2xl mx-8">
              <SearchBar
                value={filters.search}
                onChange={(value) => updateFilters({ search: value })}
                placeholder="Buscar productos, tiendas..."
              />
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              {/* Filters Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="hidden md:flex items-center"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                Filtros
              </Button>

              {/* Favorites */}
              <Button
                variant="outline"
                size="sm"
                className="relative"
              >
                <HeartIcon className="h-5 w-5" />
                {favorites.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {favorites.length}
                  </span>
                )}
              </Button>

              {/* Cart */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCart(true)}
                className="relative"
              >
                <ShoppingCartIcon className="h-5 w-5" />
                {cart && cart.itemCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cart.itemCount}
                  </span>
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <aside className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Filtros</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-blue-600 hover:text-blue-700"
                >
                  Limpiar
                </Button>
              </div>

              <ProductFilters
                categories={categories}
                filters={filters}
                onFiltersChange={updateFilters}
              />
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {filters.search ? `Resultados para "${filters.search}"` : 'Todos los productos'}
                </h2>
                <p className="text-gray-600 mt-1">
                  {pagination.totalProducts} productos encontrados
                </p>
              </div>

              {/* Sort Options */}
              <div className="flex items-center space-x-4">
                <select
                  value={`${filters.sortBy}-${filters.sortOrder}`}
                  onChange={(e) => {
                    const [sortBy, sortOrder] = e.target.value.split('-');
                    updateFilters({ sortBy: sortBy as any, sortOrder: sortOrder as any });
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="popularity-desc">Más populares</option>
                  <option value="price-asc">Precio: menor a mayor</option>
                  <option value="price-desc">Precio: mayor a menor</option>
                  <option value="rating-desc">Mejor calificados</option>
                  <option value="name-asc">Nombre: A-Z</option>
                  <option value="name-desc">Nombre: Z-A</option>
                </select>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            )}

            {/* Products Grid */}
            {!loading && (
              <>
                <ProductGrid
                  products={products}
                  favorites={favorites}
                  onAddToCart={handleAddToCart}
                  onToggleFavorite={handleToggleFavorite}
                />

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <nav className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => changePage(pagination.currentPage - 1)}
                        disabled={pagination.currentPage === 1}
                      >
                        Anterior
                      </Button>

                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
                        <Button
                          key={page}
                          variant={page === pagination.currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => changePage(page)}
                          className={page === pagination.currentPage ? "bg-blue-600 text-white" : ""}
                        >
                          {page}
                        </Button>
                      ))}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => changePage(pagination.currentPage + 1)}
                        disabled={pagination.currentPage === pagination.totalPages}
                      >
                        Siguiente
                      </Button>
                    </nav>
                  </div>
                )}
              </>
            )}

            {/* Empty State */}
            {!loading && products.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-gray-600 mb-6">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <Button
                  onClick={clearFilters}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Limpiar filtros
                </Button>
              </div>
            )}
          </main>
        </div>
      </div>

      {/* Cart Sidebar */}
      <CartSidebar
        isOpen={showCart}
        onClose={() => setShowCart(false)}
      />

      {/* Mobile Filters Toggle */}
      <div className="lg:hidden fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setShowFilters(!showFilters)}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg"
        >
          <AdjustmentsHorizontalIcon className="h-6 w-6" />
        </Button>
      </div>

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  );
}
