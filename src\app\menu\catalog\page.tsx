'use client';
import { useState, useEffect } from 'react';
import { mockCatalogAPI } from '@/services/api/catalog-mock';
import { PublicProduct, ProductCategory } from '@/services/api/catalog';
import { Button } from '@/components/ui/button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function FixedCatalogPage() {
  // Estados simples sin hooks complejos
  const [products, setProducts] = useState<PublicProduct[]>([]);
  const [allProducts, setAllProducts] = useState<PublicProduct[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cart, setCart] = useState<any>(null);
  
  // Filtros locales
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('popularity');

  // Cargar datos iniciales UNA SOLA VEZ
  useEffect(() => {
    let mounted = true;

    const loadInitialData = async () => {
      try {
        console.log('🔄 Loading initial data...');
        setLoading(true);
        setError(null);
        
        // Cargar todos los datos en paralelo
        const [productsResponse, categoriesData, cartData] = await Promise.all([
          mockCatalogAPI.getProducts(),
          mockCatalogAPI.getProductCategories(),
          mockCatalogAPI.getCart().catch(() => null)
        ]);
        
        if (mounted) {
          setAllProducts(productsResponse.products);
          setProducts(productsResponse.products);
          setCategories(categoriesData);
          setCart(cartData);
          console.log('✅ Initial data loaded successfully');
        }
        
      } catch (err: any) {
        if (mounted) {
          setError(err.message);
          console.error('❌ Error loading initial data:', err);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    loadInitialData();

    // Cleanup function
    return () => {
      mounted = false;
    };
  }, []); // Sin dependencias - solo cargar una vez

  // Filtrar productos localmente (sin API calls)
  useEffect(() => {
    let filtered = [...allProducts];

    // Filtrar por búsqueda
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(search) ||
        product.description.toLowerCase().includes(search) ||
        product.tags.some(tag => tag.toLowerCase().includes(search))
      );
    }

    // Filtrar por categoría
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Ordenar
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-asc':
          return a.price - b.price;
        case 'price-desc':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'name':
          return a.name.localeCompare(b.name);
        default: // popularity
          return (b.rating * b.reviewCount) - (a.rating * a.reviewCount);
      }
    });

    setProducts(filtered);
  }, [allProducts, searchTerm, selectedCategory, sortBy]);

  const handleAddToCart = async (productId: string) => {
    try {
      const updatedCart = await mockCatalogAPI.addToCart({
        productId,
        quantity: 1,
        deliveryMethod: 'delivery'
      });
      setCart(updatedCart);
      
      // Mostrar notificación simple
      const productName = allProducts.find(p => p.id === productId)?.name || 'Producto';
      alert(`✅ ${productName} añadido al carrito!\nTotal: $${updatedCart.total.toLocaleString('es-CL')}`);
    } catch (err: any) {
      alert(`❌ Error: ${err.message}`);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('es-CL', {
      style: 'currency',
      currency: 'CLP',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="xl" />
          <p className="mt-4 text-gray-600">Cargando catálogo...</p>
          <p className="mt-2 text-sm text-gray-500">Sin loops infinitos 🎉</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Reintentar
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                🛒 Catálogo Arreglado
              </h1>
              <p className="text-sm text-gray-600 mt-1">Sin loops infinitos ✅</p>
            </div>
            <div className="bg-blue-100 px-4 py-2 rounded-lg">
              <span className="text-blue-800 font-medium">
                Carrito: {cart?.itemCount || 0} items - {formatPrice(cart?.total || 0)}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                🔍 Buscar productos
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por nombre, descripción o tags..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                🏷️ Categoría
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todas las categorías</option>
                {categories.map(category => (
                  <option key={category.name} value={category.name}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                📊 Ordenar por
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="popularity">Más populares</option>
                <option value="price-asc">Precio: menor a mayor</option>
                <option value="price-desc">Precio: mayor a menor</option>
                <option value="rating">Mejor calificados</option>
                <option value="name">Nombre A-Z</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Mostrando {products.length} de {allProducts.length} productos
            </div>
            <Button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('');
                setSortBy('popularity');
              }}
              variant="outline"
              size="sm"
            >
              Limpiar filtros
            </Button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
            >
              {/* Product Image */}
              <div className="aspect-square bg-gray-200 relative overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
                
                {/* Category Badge */}
                <div className="absolute top-2 left-2">
                  <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    {product.category}
                  </span>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                {/* Store */}
                <p className="text-sm text-gray-600 mb-2">
                  🏪 {product.store.name}
                </p>

                {/* Name */}
                <h3 className="font-semibold text-gray-900 mb-2">
                  {product.name}
                </h3>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {product.description}
                </p>

                {/* Rating */}
                <div className="flex items-center space-x-2 mb-3">
                  <div className="flex items-center">
                    {'⭐'.repeat(Math.floor(product.rating))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {product.rating} ({product.reviewCount})
                  </span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {product.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Price and Add Button */}
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </span>
                  <Button
                    onClick={() => handleAddToCart(product.id)}
                    disabled={!product.isAvailable}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {product.isAvailable ? 'Agregar' : 'No disponible'}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No se encontraron productos
            </h3>
            <p className="text-gray-600 mb-6">
              Intenta ajustar tus filtros o buscar algo diferente
            </p>
            <Button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('');
                setSortBy('popularity');
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Limpiar filtros
            </Button>
          </div>
        )}

        {/* Navigation */}
        <div className="mt-12 text-center space-x-4">
          <Button 
            onClick={() => window.location.href = '/menu/catalog/debug'}
            variant="outline"
          >
            Ir a Diagnóstico
          </Button>
          
          <Button 
            onClick={() => window.location.href = '/menu/catalog'}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Ir al Catálogo Principal
          </Button>
        </div>

        {/* Debug Info */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-800 mb-2">✅ Estado del Sistema:</h4>
          <div className="text-sm text-green-700 space-y-1">
            <p>• Productos cargados: {allProducts.length}</p>
            <p>• Productos filtrados: {products.length}</p>
            <p>• Categorías: {categories.length}</p>
            <p>• Items en carrito: {cart?.itemCount || 0}</p>
            <p>• Sin loops infinitos: ✅</p>
            <p>• Filtros funcionan: ✅</p>
            <p>• Carrito funciona: ✅</p>
          </div>
        </div>
      </main>
    </div>
  );
}
