'use client';
// Mock service para desarrollo sin backend
import { 
  PublicProduct, 
  ProductCategory, 
  ProductReview, 
  Cart, 
  CartItem, 
  CreateOrderData, 
  Order 
} from './catalog';

// Mock data chileno
const MOCK_PRODUCTS: PublicProduct[] = [
  {
    id: '1',
    name: '<PERSON>pan<PERSON>',
    description: 'Deliciosa empanada tradicional chilena rellena de carne, cebolla, huevo duro y aceitunas',
    price: 1500,
    image: 'https://images.unsplash.com/photo-1626804475297-41608ea09aeb?w=500',
    category: 'Comida Rápida',
    rating: 4.5,
    reviewCount: 23,
    preparationTime: '15-20 min',
    isAvailable: true,
    tags: ['tradicional', 'casero', 'chileno'],
    store: {
      id: 'store1',
      name: 'Empanadas La Chilena',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.3,
      deliveryTime: '25-35 min'
    },
    nutritionalInfo: {
      calories: 320,
      protein: 15,
      carbs: 35,
      fat: 12
    }
  },
  {
    id: '2',
    name: 'Completo Italiano',
    description: 'Hot dog chileno con palta, tomate y mayonesa, los colores de la bandera italiana',
    price: 2800,
    image: 'https://images.unsplash.com/photo-1612392062798-2dd8f4b8b6b4?w=500',
    category: 'Comida Rápida',
    rating: 4.2,
    reviewCount: 45,
    preparationTime: '10-15 min',
    isAvailable: true,
    tags: ['completo', 'tradicional', 'rápido'],
    store: {
      id: 'store2',
      name: 'Completos El Rápido',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.1,
      deliveryTime: '20-30 min'
    },
    nutritionalInfo: {
      calories: 450,
      protein: 18,
      carbs: 42,
      fat: 22
    }
  },
  {
    id: '3',
    name: 'Cazuela de Cordero',
    description: 'Tradicional cazuela chilena con cordero, zapallo, choclo, papas y verduras frescas',
    price: 4200,
    image: 'https://images.unsplash.com/photo-1547592180-85f173990554?w=500',
    category: 'Platos Principales',
    rating: 4.8,
    reviewCount: 67,
    preparationTime: '30-40 min',
    isAvailable: true,
    tags: ['tradicional', 'casero', 'nutritivo'],
    store: {
      id: 'store3',
      name: 'Cocina de la Abuela',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.7,
      deliveryTime: '40-50 min'
    },
    nutritionalInfo: {
      calories: 520,
      protein: 35,
      carbs: 45,
      fat: 18
    }
  },
  {
    id: '4',
    name: 'Sopaipillas con Pebre',
    description: 'Sopaipillas caseras acompañadas de pebre tradicional chileno',
    price: 1200,
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=500',
    category: 'Aperitivos',
    rating: 4.0,
    reviewCount: 34,
    preparationTime: '10-15 min',
    isAvailable: true,
    tags: ['tradicional', 'vegetariano', 'económico'],
    store: {
      id: 'store4',
      name: 'Tradiciones Chilenas',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.2,
      deliveryTime: '15-25 min'
    },
    nutritionalInfo: {
      calories: 280,
      protein: 6,
      carbs: 38,
      fat: 12
    }
  },
  {
    id: '5',
    name: 'Mote con Huesillo',
    description: 'Refrescante bebida tradicional chilena con mote de trigo y duraznos deshidratados',
    price: 1800,
    image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=500',
    category: 'Bebidas',
    rating: 4.3,
    reviewCount: 28,
    preparationTime: '5-10 min',
    isAvailable: true,
    tags: ['refrescante', 'tradicional', 'verano'],
    store: {
      id: 'store5',
      name: 'Refrescos Tradicionales',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.0,
      deliveryTime: '10-20 min'
    },
    nutritionalInfo: {
      calories: 180,
      protein: 4,
      carbs: 42,
      fat: 1
    }
  },
  {
    id: '6',
    name: 'Pastel de Choclo',
    description: 'Tradicional pastel chileno con base de carne y cubierto con choclo molido',
    price: 3500,
    image: 'https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=500',
    category: 'Platos Principales',
    rating: 4.6,
    reviewCount: 52,
    preparationTime: '25-35 min',
    isAvailable: true,
    tags: ['tradicional', 'casero', 'abundante'],
    store: {
      id: 'store3',
      name: 'Cocina de la Abuela',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100',
      rating: 4.7,
      deliveryTime: '40-50 min'
    },
    nutritionalInfo: {
      calories: 480,
      protein: 25,
      carbs: 55,
      fat: 20
    }
  }
];

const MOCK_CATEGORIES: ProductCategory[] = [
  { name: 'Comida Rápida', count: 2 },
  { name: 'Platos Principales', count: 2 },
  { name: 'Aperitivos', count: 1 },
  { name: 'Bebidas', count: 1 },
  { name: 'Postres', count: 0 },
  { name: 'Ensaladas', count: 0 }
];

const MOCK_REVIEWS: ProductReview[] = [
  {
    id: '1',
    customerName: 'María González',
    rating: 5,
    comment: '¡Excelentes empanadas! Muy sabrosas y bien rellenas. El sabor es auténtico.',
    createdAt: '2024-01-15T10:30:00Z',
    reply: 'Muchas gracias María! Nos alegra que hayas disfrutado nuestras empanadas.'
  },
  {
    id: '2',
    customerName: 'Carlos Rodríguez',
    rating: 4,
    comment: 'Muy buenas, aunque podrían estar un poco más calientes al llegar.',
    createdAt: '2024-01-10T15:45:00Z'
  }
];

// Mock cart storage
let mockCart: Cart | null = null;

// Simular delay de red
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const mockCatalogAPI = {
  // Productos
  async getProducts(params: any = {}) {
    await delay(500);
    
    let filteredProducts = [...MOCK_PRODUCTS];
    
    // Filtrar por búsqueda
    if (params.search) {
      const search = params.search.toLowerCase();
      filteredProducts = filteredProducts.filter(p => 
        p.name.toLowerCase().includes(search) ||
        p.description.toLowerCase().includes(search) ||
        p.tags.some(tag => tag.toLowerCase().includes(search))
      );
    }
    
    // Filtrar por categoría
    if (params.category) {
      filteredProducts = filteredProducts.filter(p => p.category === params.category);
    }
    
    // Filtrar por precio
    if (params.minPrice) {
      filteredProducts = filteredProducts.filter(p => p.price >= params.minPrice);
    }
    if (params.maxPrice) {
      filteredProducts = filteredProducts.filter(p => p.price <= params.maxPrice);
    }
    
    // Ordenar
    if (params.sortBy) {
      filteredProducts.sort((a, b) => {
        let aVal, bVal;
        switch (params.sortBy) {
          case 'price':
            aVal = a.price;
            bVal = b.price;
            break;
          case 'rating':
            aVal = a.rating;
            bVal = b.rating;
            break;
          case 'name':
            aVal = a.name;
            bVal = b.name;
            break;
          default:
            aVal = a.rating * a.reviewCount; // popularity
            bVal = b.rating * b.reviewCount;
        }
        
        if (params.sortOrder === 'asc') {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });
    }
    
    // Paginación
    const page = params.page || 1;
    const limit = params.limit || 12;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    return {
      products: paginatedProducts,
      totalPages: Math.ceil(filteredProducts.length / limit),
      currentPage: page,
      totalProducts: filteredProducts.length
    };
  },

  async getProductCategories() {
    await delay(200);
    return MOCK_CATEGORIES;
  },

  async getProductDetail(productId: string) {
    await delay(300);
    const product = MOCK_PRODUCTS.find(p => p.id === productId);
    if (!product) {
      throw new Error('Producto no encontrado');
    }
    return product;
  },

  // Reseñas
  async getProductReviews(productId: string, page = 1, limit = 10) {
    await delay(400);
    return {
      reviews: MOCK_REVIEWS,
      totalPages: 1,
      currentPage: 1,
      averageRating: 4.5
    };
  },

  async createReview(reviewData: any) {
    await delay(500);
    const newReview: ProductReview = {
      id: Date.now().toString(),
      customerName: reviewData.customerName,
      rating: reviewData.rating,
      comment: reviewData.comment,
      createdAt: new Date().toISOString()
    };
    return newReview;
  },

  // Carrito
  async addToCart(cartData: any) {
    await delay(300);
    
    const product = MOCK_PRODUCTS.find(p => p.id === cartData.productId);
    if (!product) {
      throw new Error('Producto no encontrado');
    }

    if (!mockCart) {
      mockCart = {
        id: 'mock-cart',
        items: [],
        subtotal: 0,
        tax: 0,
        deliveryFee: 0,
        total: 0,
        itemCount: 0
      };
    }

    // Buscar si el item ya existe
    const existingItemIndex = mockCart.items.findIndex(
      item => item.productId === cartData.productId && 
               item.deliveryMethod === cartData.deliveryMethod
    );

    if (existingItemIndex >= 0) {
      // Actualizar cantidad
      mockCart.items[existingItemIndex].quantity += cartData.quantity;
      mockCart.items[existingItemIndex].subtotal = 
        mockCart.items[existingItemIndex].quantity * product.price;
    } else {
      // Añadir nuevo item
      const newItem: CartItem = {
        id: Date.now().toString(),
        productId: cartData.productId,
        productName: product.name,
        productImage: product.image,
        price: product.price,
        quantity: cartData.quantity,
        deliveryMethod: cartData.deliveryMethod,
        subtotal: product.price * cartData.quantity
      };
      mockCart.items.push(newItem);
    }

    // Recalcular totales
    mockCart.subtotal = mockCart.items.reduce((sum, item) => sum + item.subtotal, 0);
    mockCart.tax = Math.round(mockCart.subtotal * 0.19); // IVA 19%
    mockCart.deliveryFee = mockCart.items.some(item => item.deliveryMethod === 'delivery') ? 2000 : 0;
    mockCart.total = mockCart.subtotal + mockCart.tax + mockCart.deliveryFee;
    mockCart.itemCount = mockCart.items.reduce((sum, item) => sum + item.quantity, 0);

    return mockCart;
  },

  async getCart() {
    await delay(200);
    if (!mockCart) {
      throw new Error('Carrito no encontrado');
    }
    return mockCart;
  },

  async removeFromCart(itemId: string) {
    await delay(300);
    if (!mockCart) {
      throw new Error('Carrito no encontrado');
    }

    mockCart.items = mockCart.items.filter(item => item.id !== itemId);
    
    // Recalcular totales
    mockCart.subtotal = mockCart.items.reduce((sum, item) => sum + item.subtotal, 0);
    mockCart.tax = Math.round(mockCart.subtotal * 0.19);
    mockCart.deliveryFee = mockCart.items.some(item => item.deliveryMethod === 'delivery') ? 2000 : 0;
    mockCart.total = mockCart.subtotal + mockCart.tax + mockCart.deliveryFee;
    mockCart.itemCount = mockCart.items.reduce((sum, item) => sum + item.quantity, 0);

    return mockCart;
  },

  async updateCartItem(itemId: string, quantity: number) {
    await delay(300);
    if (!mockCart) {
      throw new Error('Carrito no encontrado');
    }

    const item = mockCart.items.find(item => item.id === itemId);
    if (!item) {
      throw new Error('Item no encontrado');
    }

    item.quantity = quantity;
    item.subtotal = item.price * quantity;

    // Recalcular totales
    mockCart.subtotal = mockCart.items.reduce((sum, item) => sum + item.subtotal, 0);
    mockCart.tax = Math.round(mockCart.subtotal * 0.19);
    mockCart.deliveryFee = mockCart.items.some(item => item.deliveryMethod === 'delivery') ? 2000 : 0;
    mockCart.total = mockCart.subtotal + mockCart.tax + mockCart.deliveryFee;
    mockCart.itemCount = mockCart.items.reduce((sum, item) => sum + item.quantity, 0);

    return mockCart;
  },

  // Pedidos
  async createOrder(orderData: CreateOrderData) {
    await delay(800);
    
    const order: Order = {
      id: Date.now().toString(),
      orderNumber: `ORD-${Date.now()}`,
      status: 'confirmado',
      total: mockCart?.total || 0,
      estimatedDeliveryTime: '30-45 min'
    };

    // Limpiar carrito después del pedido
    mockCart = null;

    return order;
  },

  async getOrderStatus(orderId: string) {
    await delay(300);
    return {
      id: orderId,
      orderNumber: `ORD-${orderId}`,
      status: 'en_preparacion',
      estimatedDeliveryTime: '25-35 min'
    };
  }
};

