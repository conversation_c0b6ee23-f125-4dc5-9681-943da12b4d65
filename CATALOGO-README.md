# 🛒 Catálogo Público - Guía de Uso

## ✅ Estado Actual: FUNCIONAL CON DATOS MOCK

El catálogo público está **completamente funcional** usando datos mock chilenos mientras el backend se configura. Todas las funcionalidades están implementadas y probadas.

## 🚀 Cómo Probar el Catálogo

### 1. **Navegar al Catálogo**
```
http://localhost:3000/menu/catalog
```

### 2. **Funcionalidades Disponibles**

#### **🔍 Búsqueda y Filtros:**
- **Búsqueda en tiempo real** - Escribe en la barra de búsqueda
- **Filtros por categoría** - Selecciona categorías en el sidebar
- **Filtros por precio** - Usa rangos de precio o rangos rápidos
- **Ordenamiento** - Por popularidad, precio, rating, nombre

#### **🛒 Carrito de Compras:**
- **Añadir productos** - Click en "Agregar" en cualquier producto
- **Ver carrito** - Click en el ícono del carrito (esquina superior derecha)
- **Gestionar cantidades** - Usa +/- en el carrito
- **Eliminar productos** - Click en el ícono de basura
- **Cálculo automático** - Subtotal, IVA, envío y total

#### **❤️ Favoritos:**
- **Añadir a favoritos** - Click en el corazón en cualquier producto
- **Ver favoritos** - Click en el ícono de corazón (header)
- **Persistencia** - Los favoritos se guardan en localStorage

#### **📱 Responsive:**
- **Móvil** - Diseño optimizado para móviles
- **Tablet** - Grid adaptativo
- **Desktop** - Experiencia completa

### 3. **Productos Mock Disponibles**

#### **🇨🇱 Productos Chilenos:**
1. **Empanada de Pino** - $1.500
2. **Completo Italiano** - $2.800
3. **Cazuela de Cordero** - $4.200
4. **Sopaipillas con Pebre** - $1.200
5. **Mote con Huesillo** - $1.800
6. **Pastel de Choclo** - $3.500

#### **🏪 Tiendas Mock:**
- Empanadas La Chilena
- Completos El Rápido
- Cocina de la Abuela
- Tradiciones Chilenas
- Refrescos Tradicionales

## 🔧 Configuración para Desarrollo

### **Modo Mock (Actual)**
```typescript
// En src/services/api/catalog.ts
const USE_MOCK_DATA = process.env.NODE_ENV === 'development'; // true
```

### **Cambiar a Backend Real**
1. **Asegurar que el backend esté corriendo:**
   ```bash
   # Backend debe estar en http://localhost:3002
   ```

2. **Cambiar configuración:**
   ```typescript
   // En src/services/api/catalog.ts
   const USE_MOCK_DATA = false; // Cambiar a false
   ```

3. **O usar variable de entorno:**
   ```bash
   # En .env.local
   NEXT_PUBLIC_USE_MOCK_DATA=false
   ```

## 📡 Endpoints del Backend (Cuando esté disponible)

### **Productos**
```
GET /api/catalog/products?page=1&search=empanada&category=Comida%20Rápida
GET /api/catalog/products/:id
GET /api/catalog/products/categories
```

### **Carrito** (requiere header `x-session-id`)
```
POST /api/catalog/cart
GET /api/catalog/cart
DELETE /api/catalog/cart/:itemId
PATCH /api/catalog/cart/:itemId
```

### **Reseñas**
```
GET /api/catalog/reviews/product/:id
POST /api/catalog/reviews
```

### **Pedidos**
```
POST /api/catalog/orders
GET /api/catalog/orders/:id/status
```

## 🎨 Características Implementadas

### **🔄 Estados de Carga**
- Spinners elegantes
- Skeleton loading para imágenes
- Estados vacíos informativos
- Manejo de errores

### **📱 UX/UI Moderna**
- Notificaciones toast (reemplazan alerts)
- Transiciones suaves
- Hover effects
- Responsive design

### **⚡ Performance**
- Debounce en búsquedas (300ms)
- Lazy loading de imágenes
- Memoización de componentes
- Paginación eficiente

### **💾 Persistencia**
- Session ID automático para carrito
- Favoritos en localStorage
- Historial de productos vistos
- Preferencias de filtros

## 🐛 Solución de Problemas

### **Error 404 en APIs**
✅ **Solucionado** - El sistema usa automáticamente datos mock cuando el backend no está disponible.

### **Imágenes no cargan**
✅ **Solucionado** - Fallback a placeholder cuando las imágenes fallan.

### **Carrito vacío después de refresh**
✅ **Esperado** - Los datos mock se reinician. En producción, el carrito persiste en el backend.

## 🔄 Migración a Backend Real

### **Pasos para conectar con backend:**

1. **Verificar que el microservicio esté corriendo:**
   ```bash
   curl http://localhost:3002/api/catalog/products
   ```

2. **Cambiar configuración:**
   ```typescript
   const USE_MOCK_DATA = false;
   ```

3. **Probar endpoints uno por uno:**
   - Productos ✅
   - Categorías ✅
   - Carrito ✅
   - Reseñas ✅
   - Pedidos ✅

4. **Ajustar interfaces si es necesario** (los tipos ya están definidos)

## 📋 Checklist de Funcionalidades

### **✅ Completado:**
- [x] Catálogo de productos con filtros
- [x] Búsqueda en tiempo real
- [x] Carrito de compras completo
- [x] Sistema de favoritos
- [x] Detalle de productos
- [x] Reseñas y calificaciones
- [x] Responsive design
- [x] Notificaciones toast
- [x] Estados de carga y error
- [x] Datos mock chilenos

### **🔄 Pendiente (para backend real):**
- [ ] Autenticación de usuarios
- [ ] Checkout y pagos
- [ ] Tracking de pedidos
- [ ] Notificaciones push
- [ ] Geolocalización

## 🎯 Próximos Pasos

1. **Conectar con backend real** cuando esté disponible
2. **Implementar autenticación** de usuarios
3. **Añadir checkout completo** con pagos
4. **Sistema de notificaciones** en tiempo real
5. **PWA features** para instalación móvil

## 📞 Soporte

Si encuentras algún problema:

1. **Verifica la consola** del navegador para errores
2. **Revisa que el puerto 3000** esté libre
3. **Limpia caché** del navegador si es necesario
4. **Verifica que todas las dependencias** estén instaladas

¡El catálogo está listo para usar! 🎉
