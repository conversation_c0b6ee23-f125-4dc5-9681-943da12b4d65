'use client';
import { useState, useEffect } from 'react';
import { 
  getProducts, 
  getProductCategories, 
  getCart, 
  addToCart 
} from '@/services/api/catalog';
import { Button } from '@/components/ui/button';

export default function CatalogDebugPage() {
  const [status, setStatus] = useState<any>({
    products: { loading: false, data: null, error: null },
    categories: { loading: false, data: null, error: null },
    cart: { loading: false, data: null, error: null }
  });

  const testProducts = async () => {
    setStatus(prev => ({
      ...prev,
      products: { loading: true, data: null, error: null }
    }));

    try {
      const data = await getProducts();
      setStatus(prev => ({
        ...prev,
        products: { loading: false, data, error: null }
      }));
    } catch (error: any) {
      setStatus(prev => ({
        ...prev,
        products: { loading: false, data: null, error: error.message }
      }));
    }
  };

  const testCategories = async () => {
    setStatus(prev => ({
      ...prev,
      categories: { loading: true, data: null, error: null }
    }));

    try {
      const data = await getProductCategories();
      setStatus(prev => ({
        ...prev,
        categories: { loading: false, data, error: null }
      }));
    } catch (error: any) {
      setStatus(prev => ({
        ...prev,
        categories: { loading: false, data: null, error: error.message }
      }));
    }
  };

  const testCart = async () => {
    setStatus(prev => ({
      ...prev,
      cart: { loading: true, data: null, error: null }
    }));

    try {
      const data = await getCart();
      setStatus(prev => ({
        ...prev,
        cart: { loading: false, data, error: null }
      }));
    } catch (error: any) {
      setStatus(prev => ({
        ...prev,
        cart: { loading: false, data: null, error: error.message }
      }));
    }
  };

  const testAddToCart = async () => {
    try {
      const cartData = await addToCart({
        productId: '1',
        quantity: 1,
        deliveryMethod: 'delivery'
      });
      alert(`Producto añadido! Total: $${cartData.total}`);
      testCart(); // Refresh cart
    } catch (error: any) {
      alert(`Error: ${error.message}`);
    }
  };

  useEffect(() => {
    testProducts();
    testCategories();
    testCart();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🔧 Diagnóstico del Catálogo
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Products Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              📦 Productos
            </h2>
            
            <Button 
              onClick={testProducts}
              disabled={status.products.loading}
              className="mb-4 w-full"
            >
              {status.products.loading ? 'Cargando...' : 'Probar Productos'}
            </Button>

            <div className="text-sm">
              <p className="mb-2">
                <strong>Estado:</strong> {
                  status.products.loading ? '🔄 Cargando' :
                  status.products.error ? '❌ Error' :
                  status.products.data ? '✅ Éxito' : '⏳ Pendiente'
                }
              </p>
              
              {status.products.error && (
                <p className="text-red-600 mb-2">
                  <strong>Error:</strong> {status.products.error}
                </p>
              )}
              
              {status.products.data && (
                <div>
                  <p className="text-green-600 mb-2">
                    <strong>Productos encontrados:</strong> {status.products.data.totalProducts}
                  </p>
                  <div className="max-h-32 overflow-y-auto">
                    {status.products.data.products.slice(0, 3).map((product: any) => (
                      <p key={product.id} className="text-xs text-gray-600">
                        • {product.name} - ${product.price}
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Categories Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🏷️ Categorías
            </h2>
            
            <Button 
              onClick={testCategories}
              disabled={status.categories.loading}
              className="mb-4 w-full"
            >
              {status.categories.loading ? 'Cargando...' : 'Probar Categorías'}
            </Button>

            <div className="text-sm">
              <p className="mb-2">
                <strong>Estado:</strong> {
                  status.categories.loading ? '🔄 Cargando' :
                  status.categories.error ? '❌ Error' :
                  status.categories.data ? '✅ Éxito' : '⏳ Pendiente'
                }
              </p>
              
              {status.categories.error && (
                <p className="text-red-600 mb-2">
                  <strong>Error:</strong> {status.categories.error}
                </p>
              )}
              
              {status.categories.data && (
                <div>
                  <p className="text-green-600 mb-2">
                    <strong>Categorías encontradas:</strong> {status.categories.data.length}
                  </p>
                  <div className="max-h-32 overflow-y-auto">
                    {status.categories.data.map((category: any) => (
                      <p key={category.name} className="text-xs text-gray-600">
                        • {category.name} ({category.count})
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Cart Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🛒 Carrito
            </h2>
            
            <div className="space-y-2 mb-4">
              <Button 
                onClick={testCart}
                disabled={status.cart.loading}
                className="w-full"
                variant="outline"
              >
                {status.cart.loading ? 'Cargando...' : 'Probar Carrito'}
              </Button>
              
              <Button 
                onClick={testAddToCart}
                className="w-full"
              >
                Añadir Producto
              </Button>
            </div>

            <div className="text-sm">
              <p className="mb-2">
                <strong>Estado:</strong> {
                  status.cart.loading ? '🔄 Cargando' :
                  status.cart.error ? '❌ Error' :
                  status.cart.data ? '✅ Éxito' : '⏳ Pendiente'
                }
              </p>
              
              {status.cart.error && (
                <p className="text-red-600 mb-2">
                  <strong>Error:</strong> {status.cart.error}
                </p>
              )}
              
              {status.cart.data && (
                <div>
                  <p className="text-green-600 mb-2">
                    <strong>Items:</strong> {status.cart.data.itemCount}
                  </p>
                  <p className="text-green-600 mb-2">
                    <strong>Total:</strong> ${status.cart.data.total}
                  </p>
                  {status.cart.data.items.length > 0 && (
                    <div className="max-h-32 overflow-y-auto">
                      {status.cart.data.items.map((item: any) => (
                        <p key={item.id} className="text-xs text-gray-600">
                          • {item.productName} x{item.quantity}
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <Button 
            onClick={() => window.location.href = '/menu/catalog'}
            className="bg-blue-600 hover:bg-blue-700 text-white mr-4"
          >
            Ir al Catálogo Principal
          </Button>
          
          <Button 
            onClick={() => window.location.href = '/menu/catalog/test'}
            variant="outline"
          >
            Ir a Página de Prueba
          </Button>
        </div>
      </div>
    </div>
  );
}
