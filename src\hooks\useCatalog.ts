'use client';
import { useState, useEffect, useCallback } from 'react';
import {
  getProducts,
  getProductCategories,
  getProductDetail,
  getStoreProducts,
  PublicProduct,
  ProductCategory
} from '@/services/api/catalog';

// Hook principal para el catálogo
export const useCatalog = () => {
  const [products, setProducts] = useState<PublicProduct[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalProducts: 0
  });

  // Filtros
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minPrice: undefined as number | undefined,
    maxPrice: undefined as number | undefined,
    storeId: '',
    sortBy: 'popularity' as 'name' | 'price' | 'rating' | 'popularity',
    sortOrder: 'desc' as 'asc' | 'desc'
  });

  // Cargar productos con filtros
  const fetchProducts = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getProducts({
        page,
        limit: 12,
        ...filters
      });

      setProducts(response.products);
      setPagination({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        totalProducts: response.totalProducts
      });
    } catch (err: any) {
      setError(err.message || 'Error al cargar productos');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Cargar categorías
  const fetchCategories = useCallback(async () => {
    try {
      const categoriesData = await getProductCategories();
      setCategories(categoriesData);
    } catch (err: any) {
      console.error('Error fetching categories:', err);
    }
  }, []);

  // Actualizar filtros
  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Limpiar filtros
  const clearFilters = useCallback(() => {
    setFilters({
      search: '',
      category: '',
      minPrice: undefined,
      maxPrice: undefined,
      storeId: '',
      sortBy: 'popularity',
      sortOrder: 'desc'
    });
  }, []);

  // Cambiar página
  const changePage = useCallback((page: number) => {
    fetchProducts(page);
  }, [fetchProducts]);

  // Cargar datos iniciales
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Recargar productos cuando cambien los filtros
  useEffect(() => {
    fetchProducts(1);
  }, [fetchProducts]);

  return {
    products,
    categories,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    clearFilters,
    changePage,
    refetch: () => fetchProducts(pagination.currentPage)
  };
};

// Hook para detalle de producto
export const useProductDetail = (productId: string) => {
  const [product, setProduct] = useState<PublicProduct | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!productId) return;

    try {
      setLoading(true);
      setError(null);
      const productData = await getProductDetail(productId);
      setProduct(productData);
    } catch (err: any) {
      setError(err.message || 'Error al cargar el producto');
      console.error('Error fetching product detail:', err);
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct
  };
};

// Hook para productos de una tienda específica
export const useStoreProducts = (storeId: string) => {
  const [products, setProducts] = useState<PublicProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalProducts: 0
  });

  // Filtros específicos de la tienda
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    sortBy: 'popularity' as string,
    sortOrder: 'desc' as string
  });

  const fetchStoreProducts = useCallback(async (page: number = 1) => {
    if (!storeId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await getStoreProducts(storeId, {
        page,
        limit: 12,
        ...filters
      });

      setProducts(response.products);
      setPagination({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        totalProducts: response.totalProducts
      });
    } catch (err: any) {
      setError(err.message || 'Error al cargar productos de la tienda');
      console.error('Error fetching store products:', err);
    } finally {
      setLoading(false);
    }
  }, [storeId, filters]);

  // Actualizar filtros
  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Cambiar página
  const changePage = useCallback((page: number) => {
    fetchStoreProducts(page);
  }, [fetchStoreProducts]);

  // Cargar productos cuando cambien los filtros o el storeId
  useEffect(() => {
    fetchStoreProducts(1);
  }, [fetchStoreProducts]);

  return {
    products,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    changePage,
    refetch: () => fetchStoreProducts(pagination.currentPage)
  };
};

// Hook para búsqueda con debounce
export const useProductSearch = (initialQuery: string = '') => {
  const [query, setQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);
  const [results, setResults] = useState<PublicProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debounce del query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Buscar productos cuando cambie el query con debounce
  useEffect(() => {
    const searchProducts = async () => {
      if (!debouncedQuery.trim()) {
        setResults([]);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const response = await getProducts({
          search: debouncedQuery,
          limit: 8 // Límite menor para búsqueda rápida
        });

        setResults(response.products);
      } catch (err: any) {
        setError(err.message || 'Error en la búsqueda');
        console.error('Error searching products:', err);
      } finally {
        setLoading(false);
      }
    };

    searchProducts();
  }, [debouncedQuery]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setError(null);
  }, []);

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearSearch
  };
};
