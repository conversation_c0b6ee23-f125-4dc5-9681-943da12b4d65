'use client';
import { useState } from 'react';
import { ProductCategory } from '@/services/api/catalog';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface ProductFiltersProps {
  categories: ProductCategory[];
  filters: {
    search: string;
    category: string;
    minPrice?: number;
    maxPrice?: number;
    storeId: string;
    sortBy: string;
    sortOrder: string;
  };
  onFiltersChange: (filters: any) => void;
}

export default function ProductFilters({ 
  categories, 
  filters, 
  onFiltersChange 
}: ProductFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    dietary: true
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category: string) => {
    onFiltersChange({
      category: filters.category === category ? '' : category
    });
  };

  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const numValue = value === '' ? undefined : parseInt(value);
    onFiltersChange({
      [type === 'min' ? 'minPrice' : 'maxPrice']: numValue
    });
  };

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetariano', icon: '🥬' },
    { id: 'vegan', label: 'Vegano', icon: '🌱' },
    { id: 'gluten-free', label: 'Sin Gluten', icon: '🌾' },
    { id: 'dairy-free', label: 'Sin Lactosa', icon: '🥛' },
    { id: 'keto', label: 'Keto', icon: '🥑' },
    { id: 'low-sodium', label: 'Bajo en Sodio', icon: '🧂' }
  ];

  return (
    <div className="space-y-6">
      {/* Categories Filter */}
      <div>
        <button
          onClick={() => toggleSection('categories')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-lg font-medium text-gray-900">Categorías</h3>
          {expandedSections.categories ? (
            <ChevronUpIcon className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="h-5 w-5 text-gray-500" />
          )}
        </button>
        
        {expandedSections.categories && (
          <div className="mt-4 space-y-2">
            <button
              onClick={() => handleCategoryChange('')}
              className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                filters.category === '' 
                  ? 'bg-blue-100 text-blue-800 font-medium' 
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Todas las categorías
            </button>
            
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => handleCategoryChange(category.name)}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                  filters.category === category.name 
                    ? 'bg-blue-100 text-blue-800 font-medium' 
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span>{category.name}</span>
                  <span className="text-xs text-gray-500">({category.count})</span>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Price Range Filter */}
      <div>
        <button
          onClick={() => toggleSection('price')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-lg font-medium text-gray-900">Rango de Precio</h3>
          {expandedSections.price ? (
            <ChevronUpIcon className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="h-5 w-5 text-gray-500" />
          )}
        </button>
        
        {expandedSections.price && (
          <div className="mt-4 space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mínimo
                </label>
                <input
                  type="number"
                  min="0"
                  step="500"
                  value={filters.minPrice || ''}
                  onChange={(e) => handlePriceChange('min', e.target.value)}
                  placeholder="$0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Máximo
                </label>
                <input
                  type="number"
                  min="0"
                  step="500"
                  value={filters.maxPrice || ''}
                  onChange={(e) => handlePriceChange('max', e.target.value)}
                  placeholder="Sin límite"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            {/* Quick Price Ranges */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Rangos rápidos:</p>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { label: 'Hasta $5.000', min: 0, max: 5000 },
                  { label: '$5.000 - $10.000', min: 5000, max: 10000 },
                  { label: '$10.000 - $20.000', min: 10000, max: 20000 },
                  { label: 'Más de $20.000', min: 20000, max: undefined }
                ].map((range) => (
                  <button
                    key={range.label}
                    onClick={() => onFiltersChange({ 
                      minPrice: range.min, 
                      maxPrice: range.max 
                    })}
                    className="px-3 py-2 text-xs border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {range.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Dietary Preferences */}
      <div>
        <button
          onClick={() => toggleSection('dietary')}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-lg font-medium text-gray-900">Opciones Dietéticas</h3>
          {expandedSections.dietary ? (
            <ChevronUpIcon className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="h-5 w-5 text-gray-500" />
          )}
        </button>
        
        {expandedSections.dietary && (
          <div className="mt-4 space-y-3">
            {dietaryOptions.map((option) => (
              <label
                key={option.id}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-lg">{option.icon}</span>
                <span className="text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Active Filters Summary */}
      {(filters.category || filters.minPrice || filters.maxPrice) && (
        <div className="pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros activos:</h4>
          <div className="space-y-2">
            {filters.category && (
              <div className="flex items-center justify-between bg-blue-50 px-3 py-2 rounded-md">
                <span className="text-sm text-blue-800">
                  Categoría: {filters.category}
                </span>
                <button
                  onClick={() => handleCategoryChange('')}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </div>
            )}
            
            {(filters.minPrice || filters.maxPrice) && (
              <div className="flex items-center justify-between bg-blue-50 px-3 py-2 rounded-md">
                <span className="text-sm text-blue-800">
                  Precio: ${filters.minPrice || 0} - ${filters.maxPrice || '∞'}
                </span>
                <button
                  onClick={() => onFiltersChange({ minPrice: undefined, maxPrice: undefined })}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
