'use client';
import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, MinusIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useCart } from '@/hooks/useCart';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CartSidebar({ isOpen, onClose }: CartSidebarProps) {
  const { cart, loading, updateItem, removeItem } = useCart();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('es-CL', {
      style: 'currency',
      currency: 'CLP',
      minimumFractionDigits: 0
    }).format(price);
  };

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    try {
      if (newQuantity <= 0) {
        await removeItem(itemId);
      } else {
        await updateItem(itemId, newQuantity);
      }
    } catch (error) {
      console.error('Error updating cart:', error);
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeItem(itemId);
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  const handleCheckout = () => {
    // TODO: Implementar checkout
    alert('Funcionalidad de checkout próximamente');
    onClose();
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-300"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col bg-white shadow-xl">
                    {/* Header */}
                    <div className="flex items-center justify-between px-4 py-6 border-b border-gray-200">
                      <Dialog.Title className="text-lg font-medium text-gray-900">
                        Carrito de Compras
                      </Dialog.Title>
                      <button
                        type="button"
                        className="text-gray-400 hover:text-gray-500"
                        onClick={onClose}
                      >
                        <XMarkIcon className="h-6 w-6" />
                      </button>
                    </div>

                    {/* Content */}
                    <div className="flex-1 overflow-y-auto">
                      {loading ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                      ) : !cart || cart.items.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full px-4">
                          <div className="text-gray-400 text-6xl mb-4">🛒</div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Tu carrito está vacío
                          </h3>
                          <p className="text-gray-600 text-center mb-6">
                            Agrega algunos productos deliciosos para comenzar
                          </p>
                          <Button
                            onClick={onClose}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            Continuar Comprando
                          </Button>
                        </div>
                      ) : (
                        <div className="px-4 py-6">
                          {/* Cart Items */}
                          <div className="space-y-4">
                            {cart.items.map((item) => (
                              <div
                                key={item.id}
                                className="flex items-center space-x-4 bg-gray-50 rounded-lg p-4"
                              >
                                {/* Product Image */}
                                <div className="flex-shrink-0 w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                                  <Image
                                    src={item.productImage}
                                    alt={item.productName}
                                    width={64}
                                    height={64}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                    }}
                                  />
                                </div>

                                {/* Product Info */}
                                <div className="flex-1 min-w-0">
                                  <h4 className="text-sm font-medium text-gray-900 truncate">
                                    {item.productName}
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {formatPrice(item.price)}
                                  </p>
                                  <p className="text-xs text-gray-500 capitalize">
                                    {item.deliveryMethod === 'delivery' ? 'Entrega' : 'Retiro'}
                                  </p>
                                </div>

                                {/* Quantity Controls */}
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                    className="p-1 rounded-full bg-white border border-gray-300 hover:bg-gray-50"
                                    disabled={loading}
                                  >
                                    <MinusIcon className="h-4 w-4 text-gray-600" />
                                  </button>
                                  
                                  <span className="text-sm font-medium text-gray-900 min-w-[2rem] text-center">
                                    {item.quantity}
                                  </span>
                                  
                                  <button
                                    onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                    className="p-1 rounded-full bg-white border border-gray-300 hover:bg-gray-50"
                                    disabled={loading}
                                  >
                                    <PlusIcon className="h-4 w-4 text-gray-600" />
                                  </button>
                                </div>

                                {/* Remove Button */}
                                <button
                                  onClick={() => handleRemoveItem(item.id)}
                                  className="p-1 text-red-500 hover:text-red-700"
                                  disabled={loading}
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>

                          {/* Order Summary */}
                          <div className="mt-8 border-t border-gray-200 pt-6">
                            <div className="space-y-3">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Subtotal</span>
                                <span className="text-gray-900">{formatPrice(cart.subtotal)}</span>
                              </div>
                              
                              {cart.tax > 0 && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">IVA</span>
                                  <span className="text-gray-900">{formatPrice(cart.tax)}</span>
                                </div>
                              )}
                              
                              {cart.deliveryFee > 0 && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Envío</span>
                                  <span className="text-gray-900">{formatPrice(cart.deliveryFee)}</span>
                                </div>
                              )}
                              
                              <div className="flex justify-between text-lg font-medium border-t border-gray-200 pt-3">
                                <span className="text-gray-900">Total</span>
                                <span className="text-gray-900">{formatPrice(cart.total)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Footer */}
                    {cart && cart.items.length > 0 && (
                      <div className="border-t border-gray-200 px-4 py-6">
                        <Button
                          onClick={handleCheckout}
                          disabled={loading}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Proceder al Pago
                        </Button>
                        
                        <Button
                          onClick={onClose}
                          variant="outline"
                          className="w-full mt-3"
                        >
                          Continuar Comprando
                        </Button>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
