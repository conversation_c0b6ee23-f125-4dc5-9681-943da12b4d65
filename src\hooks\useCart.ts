'use client';
import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import {
  addToCart,
  getCart,
  removeFromCart,
  updateCartItem,
  createOrder,
  Cart,
  CartItem,
  CreateOrderData,
  Order
} from '@/services/api/catalog';

// Context para el carrito
interface CartContextType {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  addItem: (productId: string, quantity: number, deliveryMethod: 'delivery' | 'pickup') => Promise<void>;
  removeItem: (itemId: string) => Promise<void>;
  updateItem: (itemId: string, quantity: number) => Promise<void>;
  clearError: () => void;
  refreshCart: () => Promise<void>;
  checkout: (orderData: CreateOrderData) => Promise<Order>;
}

export const CartContext = createContext<CartContextType | undefined>(undefined);

// Hook principal para el carrito
export const useCart = () => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cargar carrito actual
  const fetchCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const cartData = await getCart();
      setCart(cartData);
    } catch (err: any) {
      // Si no hay carrito, no es un error crítico
      if (err.response?.status === 404) {
        setCart(null);
      } else {
        setError(err.message || 'Error al cargar el carrito');
        console.error('Error fetching cart:', err);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Añadir producto al carrito
  const addItem = useCallback(async (
    productId: string, 
    quantity: number, 
    deliveryMethod: 'delivery' | 'pickup'
  ) => {
    try {
      setLoading(true);
      setError(null);
      const updatedCart = await addToCart({ productId, quantity, deliveryMethod });
      setCart(updatedCart);
    } catch (err: any) {
      setError(err.message || 'Error al añadir producto al carrito');
      console.error('Error adding to cart:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Eliminar producto del carrito
  const removeItem = useCallback(async (itemId: string) => {
    try {
      setLoading(true);
      setError(null);
      const updatedCart = await removeFromCart(itemId);
      setCart(updatedCart);
    } catch (err: any) {
      setError(err.message || 'Error al eliminar producto del carrito');
      console.error('Error removing from cart:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Actualizar cantidad de producto en carrito
  const updateItem = useCallback(async (itemId: string, quantity: number) => {
    try {
      setLoading(true);
      setError(null);
      
      if (quantity <= 0) {
        // Si la cantidad es 0 o menor, eliminar el item
        await removeItem(itemId);
        return;
      }
      
      const updatedCart = await updateCartItem(itemId, quantity);
      setCart(updatedCart);
    } catch (err: any) {
      setError(err.message || 'Error al actualizar producto en carrito');
      console.error('Error updating cart item:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [removeItem]);

  // Limpiar error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Crear pedido desde carrito
  const checkout = useCallback(async (orderData: CreateOrderData): Promise<Order> => {
    try {
      setLoading(true);
      setError(null);
      const order = await createOrder(orderData);
      
      // Limpiar carrito después de crear el pedido
      setCart(null);
      
      return order;
    } catch (err: any) {
      setError(err.message || 'Error al procesar el pedido');
      console.error('Error creating order:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Cargar carrito al montar el componente
  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  return {
    cart,
    loading,
    error,
    addItem,
    removeItem,
    updateItem,
    clearError,
    refreshCart: fetchCart,
    checkout
  };
};

// Hook para usar el contexto del carrito
export const useCartContext = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCartContext must be used within a CartProvider');
  }
  return context;
};

// Hook para estadísticas del carrito
export const useCartStats = () => {
  const { cart } = useCart();

  const stats = {
    itemCount: cart?.itemCount || 0,
    subtotal: cart?.subtotal || 0,
    tax: cart?.tax || 0,
    deliveryFee: cart?.deliveryFee || 0,
    total: cart?.total || 0,
    isEmpty: !cart || cart.items.length === 0,
    hasItems: cart && cart.items.length > 0
  };

  return stats;
};

// Hook para validaciones del carrito
export const useCartValidation = () => {
  const { cart } = useCart();

  const validation = {
    canCheckout: cart && cart.items.length > 0 && cart.total > 0,
    hasDeliveryItems: cart?.items.some(item => item.deliveryMethod === 'delivery') || false,
    hasPickupItems: cart?.items.some(item => item.deliveryMethod === 'pickup') || false,
    mixedDeliveryMethods: cart ? 
      cart.items.some(item => item.deliveryMethod === 'delivery') && 
      cart.items.some(item => item.deliveryMethod === 'pickup') : false,
    minimumOrderMet: cart ? cart.total >= 5000 : false, // Mínimo $5.000 CLP
  };

  return validation;
};

// Hook para gestión de favoritos (localStorage)
export const useFavorites = () => {
  const [favorites, setFavorites] = useState<string[]>([]);

  // Cargar favoritos del localStorage
  useEffect(() => {
    const savedFavorites = localStorage.getItem('catalog_favorites');
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites));
      } catch (err) {
        console.error('Error parsing favorites:', err);
        setFavorites([]);
      }
    }
  }, []);

  // Guardar favoritos en localStorage
  const saveFavorites = useCallback((newFavorites: string[]) => {
    setFavorites(newFavorites);
    localStorage.setItem('catalog_favorites', JSON.stringify(newFavorites));
  }, []);

  // Añadir a favoritos
  const addToFavorites = useCallback((productId: string) => {
    const newFavorites = [...favorites, productId];
    saveFavorites(newFavorites);
  }, [favorites, saveFavorites]);

  // Eliminar de favoritos
  const removeFromFavorites = useCallback((productId: string) => {
    const newFavorites = favorites.filter(id => id !== productId);
    saveFavorites(newFavorites);
  }, [favorites, saveFavorites]);

  // Toggle favorito
  const toggleFavorite = useCallback((productId: string) => {
    if (favorites.includes(productId)) {
      removeFromFavorites(productId);
    } else {
      addToFavorites(productId);
    }
  }, [favorites, addToFavorites, removeFromFavorites]);

  // Verificar si es favorito
  const isFavorite = useCallback((productId: string) => {
    return favorites.includes(productId);
  }, [favorites]);

  // Limpiar favoritos
  const clearFavorites = useCallback(() => {
    saveFavorites([]);
  }, [saveFavorites]);

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite,
    clearFavorites,
    favoritesCount: favorites.length
  };
};

// Hook para historial de productos vistos
export const useRecentlyViewed = () => {
  const [recentlyViewed, setRecentlyViewed] = useState<string[]>([]);
  const maxItems = 10;

  // Cargar historial del localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('catalog_recently_viewed');
    if (savedHistory) {
      try {
        setRecentlyViewed(JSON.parse(savedHistory));
      } catch (err) {
        console.error('Error parsing recently viewed:', err);
        setRecentlyViewed([]);
      }
    }
  }, []);

  // Añadir producto al historial
  const addToRecentlyViewed = useCallback((productId: string) => {
    setRecentlyViewed(prev => {
      // Eliminar si ya existe
      const filtered = prev.filter(id => id !== productId);
      // Añadir al principio
      const newHistory = [productId, ...filtered];
      // Limitar a maxItems
      const limited = newHistory.slice(0, maxItems);
      
      // Guardar en localStorage
      localStorage.setItem('catalog_recently_viewed', JSON.stringify(limited));
      
      return limited;
    });
  }, [maxItems]);

  // Limpiar historial
  const clearRecentlyViewed = useCallback(() => {
    setRecentlyViewed([]);
    localStorage.removeItem('catalog_recently_viewed');
  }, []);

  return {
    recentlyViewed,
    addToRecentlyViewed,
    clearRecentlyViewed,
    recentlyViewedCount: recentlyViewed.length
  };
};


