'use client';
import { createContext, useContext, ReactNode } from 'react';
import { useToast } from '@/hooks/useToast';

interface ToastContextType {
  toasts: any[];
  addToast: (toast: any) => string;
  removeToast: (id: string) => void;
  success: (title: string, message?: string) => string;
  error: (title: string, message?: string) => string;
  warning: (title: string, message?: string) => string;
  info: (title: string, message?: string) => string;
  clearAll: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToastContext = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToastContext must be used within ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

export default function ToastProvider({ children }: ToastProviderProps) {
  const toastValue = useToast();

  return (
    <ToastContext.Provider value={toastValue}>
      {children}
    </ToastContext.Provider>
  );
}
